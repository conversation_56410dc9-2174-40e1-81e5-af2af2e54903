# 业务组合表创建脚本
# 如果已经有现有的XXL-JOB数据库，可以单独执行此脚本来添加业务组合功能

USE `xxl_job`;

CREATE TABLE `xxl_job_combination` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '业务组合主键ID',
  `name` varchar(100) NOT NULL COMMENT '业务组合名称',
  `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
  `job_ids` text NOT NULL COMMENT '任务ID列表，逗号分隔',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_job_group` (`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务组合表';
