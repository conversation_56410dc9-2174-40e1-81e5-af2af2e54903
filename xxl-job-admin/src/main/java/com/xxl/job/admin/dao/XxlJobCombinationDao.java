package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.XxlJobCombination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务组合DAO
 * <AUTHOR>
 */
@Mapper
public interface XxlJobCombinationDao {

    /**
     * 分页查询业务组合列表
     */
    public List<XxlJobCombination> pageList(@Param("offset") int offset,
                                           @Param("pagesize") int pagesize,
                                           @Param("name") String name);

    /**
     * 分页查询总数
     */
    public int pageListCount(@Param("offset") int offset,
                            @Param("pagesize") int pagesize,
                            @Param("name") String name);

    /**
     * 保存业务组合
     */
    public int save(XxlJobCombination combination);

    /**
     * 根据ID查询业务组合
     */
    public XxlJobCombination loadById(@Param("id") int id);

    /**
     * 更新业务组合
     */
    public int update(XxlJobCombination combination);

    /**
     * 删除业务组合
     */
    public int delete(@Param("id") int id);

    /**
     * 根据名称查询业务组合
     */
    public XxlJobCombination loadByName(@Param("name") String name);
}
