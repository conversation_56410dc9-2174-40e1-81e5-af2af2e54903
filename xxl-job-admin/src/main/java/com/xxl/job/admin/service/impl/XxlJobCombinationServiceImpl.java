package com.xxl.job.admin.service.impl;

import com.xxl.job.admin.core.model.XxlJobCombination;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.dao.XxlJobCombinationDao;
import com.xxl.job.admin.dao.XxlJobInfoDao;
import com.xxl.job.admin.service.XxlJobCombinationService;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 业务组合服务实现
 * <AUTHOR>
 */
@Service
public class XxlJobCombinationServiceImpl implements XxlJobCombinationService {

    @Resource
    private XxlJobCombinationDao xxlJobCombinationDao;

    @Resource
    private XxlJobInfoDao xxlJobInfoDao;

    @Override
    public Map<String, Object> pageList(int start, int length, int jobGroup, String name) {
        // page list
        List<XxlJobCombination> list = xxlJobCombinationDao.pageList(start, length, jobGroup, name);
        int listCount = xxlJobCombinationDao.pageListCount(start, length, jobGroup, name);

        // 为每个组合添加任务名称列表
        for (XxlJobCombination combination : list) {
            if (combination.getJobIds() != null && !combination.getJobIds().trim().isEmpty()) {
                String[] jobIdArray = combination.getJobIds().split(",");
                List<String> jobNames = new ArrayList<>();
                for (String jobIdStr : jobIdArray) {
                    try {
                        int jobId = Integer.parseInt(jobIdStr.trim());
                        XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
                        if (jobInfo != null) {
                            jobNames.add(jobInfo.getJobDesc());
                        }
                    } catch (NumberFormatException e) {
                        // 忽略无效的ID
                    }
                }
                // 将任务名称列表存储在一个临时字段中，用于前端显示
                // 这里我们可以扩展XxlJobCombination类或者在Map中添加额外信息
            }
        }

        // package result
        Map<String, Object> maps = new HashMap<String, Object>();
        maps.put("recordsTotal", listCount);        // 总记录数
        maps.put("recordsFiltered", listCount);     // 过滤后记录数
        maps.put("data", list);                     // 分页列表
        return maps;
    }

    @Override
    public ReturnT<String> add(XxlJobCombination combination) {
        // 参数校验
        if (combination.getName() == null || combination.getName().trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合名称不能为空");
        }
        if (combination.getJobIds() == null || combination.getJobIds().trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID列表不能为空");
        }

        // 检查名称在同一执行器下是否已存在
        XxlJobCombination existCombination = xxlJobCombinationDao.loadByName(combination.getName(), combination.getJobGroup());
        if (existCombination != null) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合名称在该执行器下已存在");
        }

        // 验证任务ID是否有效
        String[] jobIdArray = combination.getJobIds().split(",");
        for (String jobIdStr : jobIdArray) {
            try {
                int jobId = Integer.parseInt(jobIdStr.trim());
                XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
                if (jobInfo == null) {
                    return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID " + jobId + " 不存在");
                }
            } catch (NumberFormatException e) {
                return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID格式错误: " + jobIdStr);
            }
        }

        combination.setCreateTime(new Date());
        combination.setUpdateTime(new Date());
        xxlJobCombinationDao.save(combination);
        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> update(XxlJobCombination combination) {
        // 参数校验
        if (combination.getName() == null || combination.getName().trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合名称不能为空");
        }
        if (combination.getJobIds() == null || combination.getJobIds().trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID列表不能为空");
        }

        // 检查组合是否存在
        XxlJobCombination existCombination = xxlJobCombinationDao.loadById(combination.getId());
        if (existCombination == null) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合不存在");
        }

        // 检查名称在同一执行器下是否与其他组合重复
        XxlJobCombination nameExistCombination = xxlJobCombinationDao.loadByName(combination.getName(), combination.getJobGroup());
        if (nameExistCombination != null && nameExistCombination.getId() != combination.getId()) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合名称在该执行器下已存在");
        }

        // 验证任务ID是否有效
        String[] jobIdArray = combination.getJobIds().split(",");
        for (String jobIdStr : jobIdArray) {
            try {
                int jobId = Integer.parseInt(jobIdStr.trim());
                XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
                if (jobInfo == null) {
                    return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID " + jobId + " 不存在");
                }
            } catch (NumberFormatException e) {
                return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID格式错误: " + jobIdStr);
            }
        }

        combination.setUpdateTime(new Date());
        xxlJobCombinationDao.update(combination);
        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> remove(int id) {
        XxlJobCombination combination = xxlJobCombinationDao.loadById(id);
        if (combination == null) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合不存在");
        }

        xxlJobCombinationDao.delete(id);
        return ReturnT.SUCCESS;
    }

    @Override
    public XxlJobCombination loadById(int id) {
        return xxlJobCombinationDao.loadById(id);
    }
}
