package com.xxl.job.admin.service;

import com.xxl.job.admin.core.model.XxlJobCombination;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.Map;

/**
 * 业务组合服务接口
 * <AUTHOR>
 */
public interface XxlJobCombinationService {

    /**
     * 分页查询业务组合列表
     */
    public Map<String, Object> pageList(int start, int length, String name);

    /**
     * 添加业务组合
     */
    public ReturnT<String> add(XxlJobCombination combination);

    /**
     * 更新业务组合
     */
    public ReturnT<String> update(XxlJobCombination combination);

    /**
     * 删除业务组合
     */
    public ReturnT<String> remove(int id);

    /**
     * 根据ID查询业务组合
     */
    public XxlJobCombination loadById(int id);
}
