package com.xxl.job.admin.controller;

import com.xxl.job.admin.controller.annotation.PermissionLimit;
import com.xxl.job.admin.core.model.XxlJobCombination;
import com.xxl.job.admin.core.model.XxlJobGroup;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.dao.XxlJobGroupDao;
import com.xxl.job.admin.dao.XxlJobInfoDao;
import com.xxl.job.admin.service.XxlJobCombinationService;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务组合管理Controller
 * <AUTHOR>
 */
@Controller
@RequestMapping("/jobcombination")
public class JobCombinationController {

    @Resource
    private XxlJobCombinationService xxlJobCombinationService;

    @Resource
    private XxlJobInfoDao xxlJobInfoDao;

    @Resource
    private XxlJobGroupDao xxlJobGroupDao;

    @RequestMapping
    public String index(Model model) {
        // 获取执行器列表
        List<XxlJobGroup> jobGroupList = xxlJobGroupDao.findAll();
        model.addAttribute("JobGroupList", jobGroupList);
        return "jobcombination/jobcombination.index";
    }

    @RequestMapping("/pageList")
    @ResponseBody
    public Map<String, Object> pageList(@RequestParam(required = false, defaultValue = "0") int start,
                                       @RequestParam(required = false, defaultValue = "10") int length,
                                       @RequestParam(required = false, defaultValue = "0") int jobGroup,
                                       String name) {
        return xxlJobCombinationService.pageList(start, length, jobGroup, name);
    }

    @RequestMapping("/add")
    @ResponseBody
    @PermissionLimit
    public ReturnT<String> add(XxlJobCombination combination) {
        return xxlJobCombinationService.add(combination);
    }

    /**
     * 获取指定执行器下的任务列表
     */
    @RequestMapping("/getJobsByGroup")
    @ResponseBody
    public ReturnT<List<Map<String, Object>>> getJobsByGroup(int jobGroup, String jobDesc) {
        try {
            List<XxlJobInfo> jobList = xxlJobInfoDao.pageList(0, 1000, jobGroup, -1, -1, jobDesc, null);
            List<Map<String, Object>> result = new ArrayList<>();
            for (XxlJobInfo job : jobList) {
                Map<String, Object> jobMap = new HashMap<>();
                jobMap.put("id", job.getId());
                jobMap.put("jobDesc", job.getJobDesc());
                jobMap.put("executorHandler", job.getExecutorHandler());
                result.add(jobMap);
            }
            return new ReturnT<List<Map<String, Object>>>(result);
        } catch (Exception e) {
            return new ReturnT<List<Map<String, Object>>>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }

    @RequestMapping("/update")
    @ResponseBody
    @PermissionLimit
    public ReturnT<String> update(XxlJobCombination combination) {
        return xxlJobCombinationService.update(combination);
    }

    @RequestMapping("/remove")
    @ResponseBody
    @PermissionLimit
    public ReturnT<String> remove(int id) {
        return xxlJobCombinationService.remove(id);
    }

    @RequestMapping("/loadById")
    @ResponseBody
    public ReturnT<XxlJobCombination> loadById(int id) {
        XxlJobCombination combination = xxlJobCombinationService.loadById(id);
        return combination != null ? new ReturnT<XxlJobCombination>(combination) : new ReturnT<XxlJobCombination>(ReturnT.FAIL_CODE, null);
    }

    /**
     * 获取组合详情，包含任务名称列表
     */
    @RequestMapping("/detail")
    @ResponseBody
    public ReturnT<Map<String, Object>> detail(int id) {
        XxlJobCombination combination = xxlJobCombinationService.loadById(id);
        if (combination == null) {
            return new ReturnT<Map<String, Object>>(ReturnT.FAIL_CODE, "业务组合不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("id", combination.getId());
        result.put("name", combination.getName());
        result.put("jobIds", combination.getJobIds());
        result.put("createTime", combination.getCreateTime());
        result.put("updateTime", combination.getUpdateTime());

        // 获取任务名称列表
        List<Map<String, Object>> jobList = new ArrayList<>();
        if (combination.getJobIds() != null && !combination.getJobIds().trim().isEmpty()) {
            String[] jobIdArray = combination.getJobIds().split(",");
            for (String jobIdStr : jobIdArray) {
                try {
                    int jobId = Integer.parseInt(jobIdStr.trim());
                    XxlJobInfo jobInfo = xxlJobInfoDao.loadById(jobId);
                    if (jobInfo != null) {
                        Map<String, Object> jobMap = new HashMap<>();
                        jobMap.put("id", jobInfo.getId());
                        jobMap.put("jobDesc", jobInfo.getJobDesc());
                        jobList.add(jobMap);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的ID
                }
            }
        }
        result.put("jobList", jobList);

        return new ReturnT<Map<String, Object>>(result);
    }

    /**
     * 创建业务组合（从任务列表页面调用）
     */
    @RequestMapping("/createFromJobs")
    @ResponseBody
    @PermissionLimit
    public ReturnT<String> createFromJobs(String name, String jobIds) {
        if (name == null || name.trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "业务组合名称不能为空");
        }
        if (jobIds == null || jobIds.trim().length() == 0) {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "请选择至少一个任务");
        }

        // 获取第一个任务的执行器ID
        String[] jobIdArray = jobIds.split(",");
        int jobGroup = 0;
        if (jobIdArray.length > 0) {
            try {
                int firstJobId = Integer.parseInt(jobIdArray[0].trim());
                XxlJobInfo firstJob = xxlJobInfoDao.loadById(firstJobId);
                if (firstJob != null) {
                    jobGroup = firstJob.getJobGroup();
                }
            } catch (NumberFormatException e) {
                return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID格式错误");
            }
        }

        XxlJobCombination combination = new XxlJobCombination();
        combination.setName(name);
        combination.setJobGroup(jobGroup);
        combination.setJobIds(jobIds);

        return xxlJobCombinationService.add(combination);
    }
}
