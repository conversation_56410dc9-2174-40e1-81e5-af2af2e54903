<!DOCTYPE html>
<html>
<head>
  	<#import "../common/common.macro.ftl" as netCommon>
	<@netCommon.commonStyle />
	<!-- DataTables -->
  	<link rel="stylesheet" href="${request.contextPath}/static/adminlte/bower_components/datatables.net-bs/css/dataTables.bootstrap.min.css">
    <title>${I18n.admin_name}</title>
</head>
<body class="hold-transition skin-blue sidebar-mini <#if cookieMap?exists && cookieMap["xxljob_adminlte_settings"]?exists && "off" == cookieMap["xxljob_adminlte_settings"].value >sidebar-collapse</#if>">
<div class="wrapper">
	<!-- header -->
	<@netCommon.commonHeader />
	<!-- left -->
	<@netCommon.commonLeft "jobcombination" />
	
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>业务组合管理</h1>
		</section>
		
		<!-- Main content -->
	    <section class="content">
	    
	    	<div class="row">
	    		<div class="col-xs-3">
	              	<div class="input-group">
	                	<span class="input-group-addon">执行器</span>
                		<select class="form-control" id="jobGroup" >
                			<option value="0">全部</option>
                			<#list JobGroupList as group>
                				<option value="${group.id}" >${group.title}</option>
                			</#list>
	                  	</select>
	              	</div>
	            </div>
	    		<div class="col-xs-3">
	              	<div class="input-group">
	                	<span class="input-group-addon">组合名称</span>
                		<input type="text" class="form-control" id="combinationName" placeholder="请输入组合名称" >
	              	</div>
	            </div>
                <div class="col-xs-2">
                    <button class="btn btn-block btn-info" id="searchBtn">搜索</button>
                </div>
                <div class="col-xs-2">
                    <button class="btn btn-block btn-success add" type="button">新增</button>
                </div>
            </div>
	    	
			<div class="row">
				<div class="col-xs-12">
					<div class="box">
			            <div class="box-body" >
			              	<table id="combination_list" class="table table-bordered table-striped" width="100%" >
				                <thead>
					            	<tr>
					            		<th name="id" >ID</th>
					                	<th name="name" >组合名称</th>
					                	<th name="jobGroup" >执行器</th>
					                  	<th name="createTime" >创建时间</th>
					                  	<th name="updateTime" >更新时间</th>
					                  	<th>操作</th>
					                </tr>
				                </thead>
				                <tbody></tbody>
				                <tfoot></tfoot>
							</table>
						</div>
					</div>
				</div>
			</div>
	    </section>
	</div>
	
	<!-- footer -->
	<@netCommon.commonFooter />
</div>

<!-- 新增.模态框 -->
<div class="modal fade" id="addModal" tabindex="-1" role="dialog"  aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
            	<h4 class="modal-title" >新增业务组合</h4>
         	</div>
         	<div class="modal-body">
				<form class="form-horizontal form" role="form" >
					<div class="form-group">
						<label for="jobGroup" class="col-sm-2 control-label">执行器<font color="red">*</font></label>
						<div class="col-sm-10">
							<select class="form-control" name="jobGroup" >
								<#list JobGroupList as group>
									<option value="${group.id}" >${group.title}</option>
								</#list>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label for="name" class="col-sm-2 control-label">组合名称<font color="red">*</font></label>
						<div class="col-sm-10"><input type="text" class="form-control" name="name" placeholder="请输入组合名称" maxlength="100" ></div>
					</div>
					<div class="form-group">
						<label for="jobIds" class="col-sm-2 control-label">任务列表<font color="red">*</font></label>
						<div class="col-sm-10">
							<div class="row">
								<div class="col-sm-6">
									<label>搜索任务</label>
									<input type="text" class="form-control" id="jobSearchInput" placeholder="输入任务名称搜索">
									<div id="jobSearchResults" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; margin-top: 5px; display: none;">
									</div>
								</div>
								<div class="col-sm-6">
									<label>已选任务</label>
									<div id="selectedJobs" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 5px;">
									</div>
									<input type="hidden" name="jobIds">
								</div>
							</div>
						</div>
					</div>
					<hr>
					<div class="form-group">
						<div class="col-sm-offset-3 col-sm-6">
							<button type="submit" class="btn btn-primary"  >保存</button>
							<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
						</div>
					</div>
				</form>
         	</div>
		</div>
	</div>
</div>

<!-- 更新.模态框 -->
<div class="modal fade" id="updateModal" tabindex="-1" role="dialog"  aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
            	<h4 class="modal-title" >更新业务组合</h4>
         	</div>
         	<div class="modal-body">
				<form class="form-horizontal form" role="form" >
					<div class="form-group">
						<label for="jobGroup" class="col-sm-2 control-label">执行器<font color="red">*</font></label>
						<div class="col-sm-10">
							<select class="form-control" name="jobGroup" >
								<#list JobGroupList as group>
									<option value="${group.id}" >${group.title}</option>
								</#list>
							</select>
						</div>
					</div>
					<div class="form-group">
						<label for="name" class="col-sm-2 control-label">组合名称<font color="red">*</font></label>
						<div class="col-sm-10"><input type="text" class="form-control" name="name" placeholder="请输入组合名称" maxlength="100" ></div>
					</div>
					<div class="form-group">
						<label for="jobIds" class="col-sm-2 control-label">任务列表<font color="red">*</font></label>
						<div class="col-sm-10">
							<div class="row">
								<div class="col-sm-6">
									<label>搜索任务</label>
									<input type="text" class="form-control" id="updateJobSearchInput" placeholder="输入任务名称搜索">
									<div id="updateJobSearchResults" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; margin-top: 5px; display: none;">
									</div>
								</div>
								<div class="col-sm-6">
									<label>已选任务</label>
									<div id="updateSelectedJobs" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 5px;">
									</div>
									<input type="hidden" name="jobIds">
								</div>
							</div>
						</div>
					</div>
					<hr>
					<div class="form-group">
						<div class="col-sm-offset-3 col-sm-6">
							<button type="submit" class="btn btn-primary"  >保存</button>
							<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                            <input type="hidden" name="id" >
						</div>
					</div>
				</form>
         	</div>
		</div>
	</div>
</div>

<!-- 详情.模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog"  aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
            	<h4 class="modal-title" >业务组合详情</h4>
         	</div>
         	<div class="modal-body">
				<form class="form-horizontal" role="form" >
					<div class="form-group">
						<label class="col-sm-3 control-label">组合名称：</label>
						<div class="col-sm-9">
							<p class="form-control-static" id="detail-name"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">执行器：</label>
						<div class="col-sm-9">
							<p class="form-control-static" id="detail-jobGroup"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">创建时间：</label>
						<div class="col-sm-9">
							<p class="form-control-static" id="detail-createTime"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">更新时间：</label>
						<div class="col-sm-9">
							<p class="form-control-static" id="detail-updateTime"></p>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-3 control-label">包含任务：</label>
						<div class="col-sm-9">
							<div id="detail-jobList"></div>
						</div>
					</div>
					<hr>
					<div class="form-group">
						<div class="col-sm-offset-3 col-sm-6">
							<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						</div>
					</div>
				</form>
         	</div>
		</div>
	</div>
</div>

<@netCommon.commonScript />
<!-- DataTables -->
<script src="${request.contextPath}/static/adminlte/bower_components/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="${request.contextPath}/static/adminlte/bower_components/datatables.net-bs/js/dataTables.bootstrap.min.js"></script>
<!-- moment -->
<script src="${request.contextPath}/static/adminlte/bower_components/moment/moment.min.js"></script>
<script src="${request.contextPath}/static/js/jobcombination.index.1.js"></script>
</body>
</html>
