<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobCombinationDao">
	
	<resultMap id="XxlJobCombination" type="com.xxl.job.admin.core.model.XxlJobCombination" >
		<result column="id" property="id" />
	    <result column="name" property="name" />
	    <result column="job_ids" property="jobIds" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.name,
		t.job_ids,
		t.create_time,
		t.update_time
	</sql>

	<select id="pageList" parameterType="java.util.HashMap" resultMap="XxlJobCombination">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_combination AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="name != null and name != ''">
				AND t.name like CONCAT(CONCAT('%', #{name}), '%')
			</if>
		</trim>
		ORDER BY t.create_time DESC
		LIMIT #{offset}, #{pagesize}
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM xxl_job_combination AS t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="name != null and name != ''">
				AND t.name like CONCAT(CONCAT('%', #{name}), '%')
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.xxl.job.admin.core.model.XxlJobCombination" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_combination (
			name,
			job_ids,
			create_time,
			update_time
		) VALUES (
			#{name},
			#{jobIds},
			#{createTime},
			#{updateTime}
		);
	</insert>

	<select id="loadById" parameterType="java.util.HashMap" resultMap="XxlJobCombination">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_combination AS t
		WHERE t.id = #{id}
	</select>

	<select id="loadByName" parameterType="java.util.HashMap" resultMap="XxlJobCombination">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_combination AS t
		WHERE t.name = #{name}
	</select>

	<update id="update" parameterType="com.xxl.job.admin.core.model.XxlJobCombination" >
		UPDATE xxl_job_combination
		SET name = #{name},
			job_ids = #{jobIds},
			update_time = #{updateTime}
		WHERE id = #{id}
	</update>

	<delete id="delete" parameterType="java.lang.Integer" >
		DELETE FROM xxl_job_combination
		WHERE id = #{id}
	</delete>

</mapper>
