$(function() {

	// init date tables
	var combinationTable = $("#combination_list").dataTable({
		"deferRender": true,
		"processing" : true,
	    "serverSide": true,
		"ajax": {
			url: base_url + "/jobcombination/pageList",
			type:"post",
	        data : function ( d ) {
	        	var obj = {};
	        	obj.start = d.start;
	        	obj.length = d.length;
	        	obj.jobGroup = $('#jobGroup').val();
	        	obj.name = $('#combinationName').val();
	        	return obj;
	        }
	    },
	    "searching": false,
	    "ordering": false,
	    //"scrollX": true,	// scroll x，close self-adaption
	    "columns": [
	                { "data": 'id', "bSortable": false, "visible" : true, "width":'8%'},
	                { "data": 'name', "bSortable": false, "visible" : true, "width":'25%'},
	                {
	                	"data": 'createTime',
	                	"bSortable": false,
	                	"visible" : true,
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
	                {
	                	"data": 'updateTime',
	                	"bSortable": false,
	                	"visible" : true,
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
	                {
	                	"data": I18n.system_opt ,
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return function(){
	                			// data
	                			tableData['key'+row.id] = row;

	                			// opt
	                			var html = '<p id="'+ row.id +'" >'+
	                				'<button class="btn btn-warning btn-xs detail" type="button">详情</button>  '+
	                				'<button class="btn btn-primary btn-xs update" type="button">更新</button>  '+
	                				'<button class="btn btn-danger btn-xs remove" type="button">删除</button>  '+
	                				'</p>';

	                			return html;
	                		};
	                	}
	                }
	            ],
		"language" : {
			"sProcessing" : "处理中...",
			"sLengthMenu" : "每页 _MENU_ 条记录",
			"sZeroRecords" : "没有匹配结果",
			"sInfo" : "第 _PAGE_ 页 ( 总共 _PAGES_ 页，_TOTAL_ 条记录 )",
			"sInfoEmpty" : "无记录",
			"sInfoFiltered" : "(由 _MAX_ 项结果过滤)",
			"sInfoPostFix" : "",
			"sSearch" : "搜索:",
			"sUrl" : "",
			"sEmptyTable" : "表中数据为空",
			"sLoadingRecords" : "载入中...",
			"sInfoThousands" : ",",
			"oPaginate" : {
				"sFirst" : "首页",
				"sPrevious" : "上页",
				"sNext" : "下页",
				"sLast" : "末页"
			},
			"oAria" : {
				"sSortAscending" : ": 以升序排列此列",
				"sSortDescending" : ": 以降序排列此列"
			}
		}
	});

    // table data
    var tableData = {};

	// search btn
	$('#searchBtn').on('click', function(){
		combinationTable.fnDraw();
	});

	// 执行器下拉框变化事件
	$('#jobGroup').on('change', function(){
		combinationTable.fnDraw();
	});

	// add
	$(".add").click(function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
        	name : {
        		required : true,
        		maxlength: 100
        	},
        	jobIds : {
        		required : true
        	}
        },
        messages : {
        	name : {
        		required :"请输入组合名称"
        	},
        	jobIds : {
        		required :"请输入任务ID列表"
        	}
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent().append(error);
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobcombination/add", $("#addModal .form").serialize(), function(data, status) {
        		if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.msg("新增成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "新增失败" );
				}
        	});
        }
	});
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
	});

	// update
	$("#combination_list").on('click', '.update',function() {
		var id = $(this).parent('p').attr("id");
		var row = tableData['key'+id];

		// base data
		$("#updateModal .form input[name='name']").val( row.name );
		$("#updateModal .form select[name='jobGroup']").val( row.jobGroup );
		$("#updateModal .form input[name='id']").val( row.id );

		// 加载已选任务
		loadSelectedJobs(row.id);

		// show
		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',
        errorClass : 'help-block',
        focusInvalid : true,
        rules : {
        	name : {
        		required : true,
        		maxlength: 100
        	},
        	jobIds : {
        		required : true
        	}
        },
        messages : {
        	name : {
        		required :"请输入组合名称"
        	},
        	jobIds : {
        		required :"请输入任务ID列表"
        	}
        },
		highlight : function(element) {
            $(element).closest('.form-group').addClass('has-error');
        },
        success : function(label) {
            label.closest('.form-group').removeClass('has-error');
            label.remove();
        },
        errorPlacement : function(error, element) {
            element.parent().append(error);
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobcombination/update", $("#updateModal .form").serialize(), function(data, status) {
        		if (data.code == "200") {
					$('#updateModal').modal('hide');
					layer.msg("更新成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "更新失败" );
				}
        	});
        }
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset();
		updateModalValidate.resetForm();
		$("#updateModal .form .form-group").removeClass("has-error");
	});

	// remove
	$("#combination_list").on('click', '.remove',function() {
		var id = $(this).parent('p').attr("id");
		var row = tableData['key'+id];

		layer.confirm("确认删除业务组合\"" + row.name + "\"?", {
			icon: 3,
			title: '系统提示',
			btn: ['确认', '取消']
		}, function(index){
			layer.close(index);

			$.post(base_url + "/jobcombination/remove", {"id": id}, function(data, status) {
				if (data.code == "200") {
					layer.msg("删除成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "删除失败" );
				}
			});
		});
	});

	// detail
	$("#combination_list").on('click', '.detail',function() {
		var id = $(this).parent('p').attr("id");

		$.post(base_url + "/jobcombination/detail", {"id": id}, function(data, status) {
			if (data.code == "200") {
				var combination = data.content;

				// 填充基本信息
				$("#detail-name").text(combination.name);
				$("#detail-jobGroup").text(combination.jobGroup || "");
				$("#detail-createTime").text(combination.createTime ? moment(new Date(combination.createTime)).format("YYYY-MM-DD HH:mm:ss") : "");
				$("#detail-updateTime").text(combination.updateTime ? moment(new Date(combination.updateTime)).format("YYYY-MM-DD HH:mm:ss") : "");

				// 填充任务列表
				var jobListHtml = "";
				if (combination.jobList && combination.jobList.length > 0) {
					jobListHtml = "<ul class='list-unstyled'>";
					for (var i = 0; i < combination.jobList.length; i++) {
						var job = combination.jobList[i];
						jobListHtml += "<li><strong>ID:" + job.id + "</strong> - " + job.jobDesc + "</li>";
					}
					jobListHtml += "</ul>";
				} else {
					jobListHtml = "<p class='text-muted'>无任务</p>";
				}
				$("#detail-jobList").html(jobListHtml);

				// 显示模态框
				$('#detailModal').modal({backdrop: false, keyboard: false}).modal('show');
			} else {
				layer.msg( data.msg || "获取详情失败" );
			}
		});
	});

	// 新增模态框中的任务搜索功能
	$("#addModal").on('show.bs.modal', function () {
		// 清空搜索结果
		$("#jobSearchResults").hide().empty();
		$("#selectedJobs").empty();
		$("#addModal input[name='jobIds']").val("");
	});

	// 新增模态框中的任务搜索（按回车键触发）
	$("#jobSearchInput").on('keypress', function(e) {
		if (e.which == 13) { // 回车键
			searchJobs('#addModal', '#jobSearchInput', '#jobSearchResults');
		}
	});

	// 搜索任务的通用函数
	function searchJobs(modalSelector, inputSelector, resultSelector) {
		var jobGroup = $(modalSelector + " select[name='jobGroup']").val();
		var searchText = $(inputSelector).val().trim();

		if (!jobGroup) {
			layer.msg("请先选择执行器");
			return;
		}

		if (searchText.length < 1) {
			$(resultSelector).hide();
			return;
		}

		// 搜索任务
		$.post(base_url + "/jobinfo/pageList", {
			start: 0,
			length: 20,
			jobGroup: jobGroup,
			triggerStatus: -1,
			envFlag: -1,
			jobDesc: searchText,
			executorHandler: ""
		}, function(data) {
			if (data.data && data.data.length > 0) {
				var html = "";
				var btnClass = modalSelector === '#addModal' ? 'add-job-btn' : 'add-job-btn-update';
				for (var i = 0; i < data.data.length; i++) {
					var job = data.data[i];
					html += "<div class='job-item' data-id='" + job.id + "' data-desc='" + job.jobDesc + "' style='padding: 5px; border-bottom: 1px solid #eee; cursor: pointer;'>";
					html += "<strong>ID:" + job.id + "</strong> - " + job.jobDesc;
					html += "<button type='button' class='btn btn-xs btn-success pull-right " + btnClass + "'>添加</button>";
					html += "</div>";
				}
				$(resultSelector).html(html).show();
			} else {
				$(resultSelector).html("<p class='text-muted' style='padding: 10px;'>未找到相关任务</p>").show();
			}
		});
	}

	// 添加任务到已选列表
	$(document).on('click', '.add-job-btn', function() {
		var jobItem = $(this).closest('.job-item');
		var jobId = jobItem.data('id');
		var jobDesc = jobItem.data('desc');

		// 检查是否已存在
		if ($("#selectedJobs .selected-job[data-id='" + jobId + "']").length > 0) {
			layer.msg("任务已存在");
			return;
		}

		// 添加到已选列表
		var selectedHtml = "<div class='selected-job' data-id='" + jobId + "' style='padding: 5px; border-bottom: 1px solid #eee;'>";
		selectedHtml += "<strong>ID:" + jobId + "</strong> - " + jobDesc;
		selectedHtml += "<button type='button' class='btn btn-xs btn-danger pull-right remove-job-btn'>删除</button>";
		selectedHtml += "</div>";
		$("#selectedJobs").append(selectedHtml);

		// 更新隐藏字段
		updateJobIds('#addModal');
	});

	// 从已选列表中删除任务
	$(document).on('click', '.remove-job-btn', function() {
		$(this).closest('.selected-job').remove();
		// 更新隐藏字段
		updateJobIds('#addModal');
	});

	// 更新任务ID列表
	function updateJobIds(modalSelector) {
		var jobIds = [];
		$(modalSelector + " .selected-job").each(function() {
			jobIds.push($(this).data('id'));
		});
		$(modalSelector + " input[name='jobIds']").val(jobIds.join(','));
	}

	// 加载已选任务
	function loadSelectedJobs(combinationId) {
		$("#updateSelectedJobs").empty();

		$.post(base_url + "/jobcombination/detail", {"id": combinationId}, function(data) {
			if (data.code == "200" && data.content.jobList) {
				var jobList = data.content.jobList;
				for (var i = 0; i < jobList.length; i++) {
					var job = jobList[i];
					var selectedHtml = "<div class='selected-job' data-id='" + job.id + "' style='padding: 5px; border-bottom: 1px solid #eee;'>";
					selectedHtml += "<strong>ID:" + job.id + "</strong> - " + job.jobDesc;
					selectedHtml += "<button type='button' class='btn btn-xs btn-danger pull-right remove-job-btn-update'>删除</button>";
					selectedHtml += "</div>";
					$("#updateSelectedJobs").append(selectedHtml);
				}
				// 更新隐藏字段
				updateJobIds('#updateModal');
			}
		});
	}

	// 更新模态框中的任务搜索功能
	$("#updateModal").on('show.bs.modal', function () {
		// 清空搜索结果
		$("#updateJobSearchResults").hide().empty();
	});

	// 更新模态框中的任务搜索（按回车键触发）
	$("#updateJobSearchInput").on('keypress', function(e) {
		if (e.which == 13) { // 回车键
			searchJobs('#updateModal', '#updateJobSearchInput', '#updateJobSearchResults');
		}
	});

	// 更新模态框中添加任务
	$(document).on('click', '.add-job-btn-update', function() {
		var jobItem = $(this).closest('.job-item');
		var jobId = jobItem.data('id');
		var jobDesc = jobItem.data('desc');

		// 检查是否已存在
		if ($("#updateSelectedJobs .selected-job[data-id='" + jobId + "']").length > 0) {
			layer.msg("任务已存在");
			return;
		}

		// 添加到已选列表
		var selectedHtml = "<div class='selected-job' data-id='" + jobId + "' style='padding: 5px; border-bottom: 1px solid #eee;'>";
		selectedHtml += "<strong>ID:" + jobId + "</strong> - " + jobDesc;
		selectedHtml += "<button type='button' class='btn btn-xs btn-danger pull-right remove-job-btn-update'>删除</button>";
		selectedHtml += "</div>";
		$("#updateSelectedJobs").append(selectedHtml);

		// 更新隐藏字段
		updateJobIds('#updateModal');
	});

	// 更新模态框中从已选列表中删除任务
	$(document).on('click', '.remove-job-btn-update', function() {
		$(this).closest('.selected-job').remove();
		// 更新隐藏字段
		updateJobIds('#updateModal');
	});

});
