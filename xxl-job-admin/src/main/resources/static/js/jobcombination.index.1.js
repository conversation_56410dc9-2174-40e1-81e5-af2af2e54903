$(function() {

	// init date tables
	var combinationTable = $("#combination_list").dataTable({
		"deferRender": true,
		"processing" : true, 
	    "serverSide": true,
		"ajax": {
			url: base_url + "/jobcombination/pageList",
			type:"post",
	        data : function ( d ) {
	        	var obj = {};
	        	obj.start = d.start;
	        	obj.length = d.length;
	        	obj.name = $('#combinationName').val();
	        	return obj;
	        }
	    },
	    "searching": false,
	    "ordering": false,
	    //"scrollX": true,	// scroll x，close self-adaption
	    "columns": [
	                { "data": 'id', "bSortable": false, "visible" : true, "width":'10%'},
	                { "data": 'name', "bSortable": false, "visible" : true, "width":'30%'},
	                { 
	                	"data": 'createTime', 
	                	"bSortable": false, 
	                	"visible" : true, 
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
	                { 
	                	"data": 'updateTime', 
	                	"bSortable": false, 
	                	"visible" : true, 
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return data?moment(new Date(data)).format("YYYY-MM-DD HH:mm:ss"):"";
	                	}
	                },
	                { 
	                	"data": '操作' ,
	                	"width":'20%',
	                	"render": function ( data, type, row ) {
	                		return function(){
	                			// data
	                			tableData['key'+row.id] = row;
	                			
	                			// opt
	                			var html = '<p id="'+ row.id +'" >'+
	                				'<button class="btn btn-warning btn-xs detail" type="button">详情</button>  '+
	                				'<button class="btn btn-primary btn-xs update" type="button">更新</button>  '+
	                				'<button class="btn btn-danger btn-xs remove" type="button">删除</button>  '+
	                				'</p>';
	                			
	                			return html;
	                		};
	                	}
	                }
	            ],
		"language" : {
			"sProcessing" : "处理中...",
			"sLengthMenu" : "每页 _MENU_ 条记录",
			"sZeroRecords" : "没有匹配结果",
			"sInfo" : "第 _PAGE_ 页 ( 总共 _PAGES_ 页，_TOTAL_ 条记录 )",
			"sInfoEmpty" : "无记录",
			"sInfoFiltered" : "(由 _MAX_ 项结果过滤)",
			"sInfoPostFix" : "",
			"sSearch" : "搜索:",
			"sUrl" : "",
			"sEmptyTable" : "表中数据为空",
			"sLoadingRecords" : "载入中...",
			"sInfoThousands" : ",",
			"oPaginate" : {
				"sFirst" : "首页",
				"sPrevious" : "上页",
				"sNext" : "下页",
				"sLast" : "末页"
			},
			"oAria" : {
				"sSortAscending" : ": 以升序排列此列",
				"sSortDescending" : ": 以降序排列此列"
			}
		}
	});

    // table data
    var tableData = {};

	// search btn
	$('#searchBtn').on('click', function(){
		combinationTable.fnDraw();
	});

	// add
	$(".add").click(function(){
		$('#addModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var addModalValidate = $("#addModal .form").validate({
		errorElement : 'span',  
        errorClass : 'help-block',
        focusInvalid : true,  
        rules : {
        	name : {
        		required : true,
        		maxlength: 100
        	},
        	jobIds : {
        		required : true
        	}
        }, 
        messages : {  
        	name : {
        		required :"请输入组合名称"
        	},
        	jobIds : {
        		required :"请输入任务ID列表"
        	}
        },
		highlight : function(element) {  
            $(element).closest('.form-group').addClass('has-error');  
        },
        success : function(label) {  
            label.closest('.form-group').removeClass('has-error');  
            label.remove();  
        },
        errorPlacement : function(error, element) {  
            element.parent().append(error);  
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobcombination/add", $("#addModal .form").serialize(), function(data, status) {
        		if (data.code == "200") {
					$('#addModal').modal('hide');
					layer.msg("新增成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "新增失败" );
				}
        	});
        }
	});
	$("#addModal").on('hide.bs.modal', function () {
		$("#addModal .form")[0].reset();
		addModalValidate.resetForm();
		$("#addModal .form .form-group").removeClass("has-error");
	});

	// update
	$("#combination_list").on('click', '.update',function() {
		var id = $(this).parent('p').attr("id");
		var row = tableData['key'+id];

		// base data
		$("#updateModal .form input[name='name']").val( row.name );
		$("#updateModal .form textarea[name='jobIds']").val( row.jobIds );
		$("#updateModal .form input[name='id']").val( row.id );

		// show
		$('#updateModal').modal({backdrop: false, keyboard: false}).modal('show');
	});
	var updateModalValidate = $("#updateModal .form").validate({
		errorElement : 'span',  
        errorClass : 'help-block',
        focusInvalid : true,  
        rules : {
        	name : {
        		required : true,
        		maxlength: 100
        	},
        	jobIds : {
        		required : true
        	}
        }, 
        messages : {  
        	name : {
        		required :"请输入组合名称"
        	},
        	jobIds : {
        		required :"请输入任务ID列表"
        	}
        },
		highlight : function(element) {  
            $(element).closest('.form-group').addClass('has-error');  
        },
        success : function(label) {  
            label.closest('.form-group').removeClass('has-error');  
            label.remove();  
        },
        errorPlacement : function(error, element) {  
            element.parent().append(error);  
        },
        submitHandler : function(form) {
        	$.post(base_url + "/jobcombination/update", $("#updateModal .form").serialize(), function(data, status) {
        		if (data.code == "200") {
					$('#updateModal').modal('hide');
					layer.msg("更新成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "更新失败" );
				}
        	});
        }
	});
	$("#updateModal").on('hide.bs.modal', function () {
		$("#updateModal .form")[0].reset();
		updateModalValidate.resetForm();
		$("#updateModal .form .form-group").removeClass("has-error");
	});

	// remove
	$("#combination_list").on('click', '.remove',function() {
		var id = $(this).parent('p').attr("id");
		var row = tableData['key'+id];

		layer.confirm("确认删除业务组合\"" + row.name + "\"?", {
			icon: 3,
			title: '系统提示',
			btn: ['确认', '取消']
		}, function(index){
			layer.close(index);

			$.post(base_url + "/jobcombination/remove", {"id": id}, function(data, status) {
				if (data.code == "200") {
					layer.msg("删除成功");
					combinationTable.fnDraw();
				} else {
					layer.msg( data.msg || "删除失败" );
				}
			});
		});
	});

	// detail
	$("#combination_list").on('click', '.detail',function() {
		var id = $(this).parent('p').attr("id");
		
		$.post(base_url + "/jobcombination/detail", {"id": id}, function(data, status) {
			if (data.code == "200") {
				var combination = data.content;
				
				// 填充基本信息
				$("#detail-name").text(combination.name);
				$("#detail-createTime").text(combination.createTime ? moment(new Date(combination.createTime)).format("YYYY-MM-DD HH:mm:ss") : "");
				$("#detail-updateTime").text(combination.updateTime ? moment(new Date(combination.updateTime)).format("YYYY-MM-DD HH:mm:ss") : "");
				
				// 填充任务列表
				var jobListHtml = "";
				if (combination.jobList && combination.jobList.length > 0) {
					jobListHtml = "<ul class='list-unstyled'>";
					for (var i = 0; i < combination.jobList.length; i++) {
						var job = combination.jobList[i];
						jobListHtml += "<li><strong>ID:" + job.id + "</strong> - " + job.jobDesc + "</li>";
					}
					jobListHtml += "</ul>";
				} else {
					jobListHtml = "<p class='text-muted'>无任务</p>";
				}
				$("#detail-jobList").html(jobListHtml);
				
				// 显示模态框
				$('#detailModal').modal({backdrop: false, keyboard: false}).modal('show');
			} else {
				layer.msg( data.msg || "获取详情失败" );
			}
		});
	});

});
